<script setup lang="ts">
import resourceOverview from '@/views/comprehensiveSituationPresentation/assets/资源概览-标题@3x.png'
import resourceLoadSituation from '@/views/comprehensiveSituationPresentation/assets/资源负载态势-标题@3x.png'
import systemTopology from '@/views/comprehensiveSituationPresentation/assets/系统架构-标题@3x(1).png'
import userUsageSituation from '@/views/comprehensiveSituationPresentation/assets/用户使用态势-标题@3x.png'
import documentUsageSituation from '@/views/comprehensiveSituationPresentation/assets/公文应用态势-标题@3x.png'
import systemRunSituation from '@/views/comprehensiveSituationPresentation/assets/系统运行态势标题@3x.png'
import departmentRanking from '@/views/comprehensiveSituationPresentation/assets/部门公文处理排名@3x.png'
import hotFunctionTop5 from '@/views/comprehensiveSituationPresentation/assets/CPU利用率趋势图-标题@3x(2).png'
import departmentTop5 from '@/views/comprehensiveSituationPresentation/assets/CPU利用率趋势图-标题@3x(1).png'
import { ref, watch } from 'vue';
const props = defineProps<{
  color?: string;
  img?: string;
  txt?: string;
}>();
// 创建一个映射对象来处理图片路径
const imageMap = {
  'resourceOverview': resourceOverview,
  'resourceLoadSituation': resourceLoadSituation,
  'systemTopology': systemTopology,
  'userUsageSituation': userUsageSituation,
  'documentUsageSituation': documentUsageSituation,
  'systemRunSituation': systemRunSituation,
  'departmentRanking': departmentRanking,
  'hotFunctionTop5': hotFunctionTop5,
  'departmentTop5': departmentTop5
};

const image = ref('');

watch(() => props.img, (newVal) => {
  // 如果传入的是映射键名，则使用映射的图片路径
  if (newVal && imageMap[newVal]) {
    image.value = imageMap[newVal];
  } else {
    // 如果是完整的导入路径，则直接使用
    image.value = newVal;
  }
}, { immediate: true });
</script>

<template>
  <div class="border-box">
    <div class="border-box-txt">
      <img class="border-box-img" :src="image" mode="scaleToFill" />
      <span>{{ txt }}</span>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.border-box {
  position: relative;
  width: 100%;

  // height: 100%;
  .border-box-txt {
    position: relative;

    span {
      position: absolute;
      left: 66px;
      top: 8px;
      font-size: 1.2rem;
      font-weight: 900;
      font-style: italic;
      background: linear-gradient(180deg,
          #333333 0%,
          #409eff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 1px;
    }
  }

  .content {
    position: relative;
    width: 100%;
    height: 100%;
  }
}
</style>